import React from "react";
import { ScrollView } from "react-native";
import { Text } from "@/components/ui/text";
import { LogTypeSelector } from "./LogTypeSelector";
import { MealTimeSelector } from "./MealTimeSelector";
import { ImageUploadSection } from "./ImageUploadSection";

type LogType = "meal" | "drink";
type MealType = "breakfast" | "lunch" | "dinner" | "snacks";

interface SetupScreenProps {
  logType: LogType;
  mealType: MealType;
  selectedImage: string | null;
  isAnalyzing: boolean;
  isUploading: boolean;
  onLogTypeChange: (type: LogType) => void;
  onMealTypeChange: (type: MealType) => void;
  onImageSelected: (uri: string) => void;
  onAnalyze: () => void;
}

export const SetupScreen: React.FC<SetupScreenProps> = ({
  logType,
  mealType,
  selectedImage,
  isAnalyzing,
  isUploading,
  onLogTypeChange,
  onMealTypeChange,
  onImageSelected,
  onAnalyze,
}) => {
  return (
    <ScrollView className="flex-1 p-6">
      <Text className="text-2xl font-bold mb-2">Log Your {logType}</Text>
      <Text className="text-muted-foreground mb-6">
        Select the type and meal time, then add a photo for AI analysis.
      </Text>

      <LogTypeSelector
        logType={logType}
        onLogTypeChange={onLogTypeChange}
      />

      <MealTimeSelector
        mealType={mealType}
        onMealTypeChange={onMealTypeChange}
      />

      <ImageUploadSection
        selectedImage={selectedImage}
        onImageSelected={onImageSelected}
        isAnalyzing={isAnalyzing}
        isUploading={isUploading}
        onAnalyze={selectedImage ? onAnalyze : undefined}
      />
    </ScrollView>
  );
};
