import React from "react";
import { View, TouchableOpacity } from "react-native";
import { Coffee, Utensils, Moon, <PERSON><PERSON> } from "lucide-react-native";
import { Card } from "@/components/ui/card";
import { Text } from "@/components/ui/text";

type MealType = "breakfast" | "lunch" | "dinner" | "snacks";

interface MealTimeSelectorProps {
  mealType: MealType;
  onMealTypeChange: (type: MealType) => void;
}

const mealTypeIcons = {
  breakfast: Coffee,
  lunch: Utensils,
  dinner: Moon,
  snacks: <PERSON>ie,
};

export const MealTimeSelector: React.FC<MealTimeSelectorProps> = ({
  mealType,
  onMealTypeChange,
}) => {
  return (
    <Card className="p-4 mb-6">
      <Text className="text-lg font-semibold mb-3">Meal Time</Text>
      <View className="grid grid-cols-2 gap-3">
        {Object.entries(mealTypeIcons).map(([type, Icon]) => (
          <TouchableOpacity
            key={type}
            onPress={() => onMealTypeChange(type as MealType)}
            className={`p-4 rounded-lg border-2 ${
              mealType === type
                ? "border-primary bg-primary/10"
                : "border-border bg-background"
            }`}
          >
            <View className="items-center">
              <Icon
                size={24}
                className={
                  mealType === type ? "text-primary" : "text-muted-foreground"
                }
              />
              <Text
                className={`mt-2 capitalize ${
                  mealType === type
                    ? "text-primary font-semibold"
                    : "text-muted-foreground"
                }`}
              >
                {type}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );
};
