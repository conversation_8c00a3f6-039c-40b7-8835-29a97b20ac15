import React from "react";
import { View, TouchableOpacity } from "react-native";
import { Card } from "@/components/ui/card";
import { Text } from "@/components/ui/text";

type LogType = "meal" | "drink";

interface LogTypeSelectorProps {
  logType: LogType;
  onLogTypeChange: (type: LogType) => void;
}

export const LogTypeSelector: React.FC<LogTypeSelectorProps> = ({
  logType,
  onLogTypeChange,
}) => {
  return (
    <Card className="p-4 mb-4">
      <Text className="text-lg font-semibold mb-3">Type</Text>
      <View className="flex-row gap-3">
        <TouchableOpacity
          onPress={() => onLogTypeChange("meal")}
          className={`flex-1 p-3 rounded-lg border-2 ${
            logType === "meal"
              ? "border-primary bg-primary/10"
              : "border-border bg-background"
          }`}
        >
          <Text
            className={`text-center font-medium ${
              logType === "meal" ? "text-primary" : "text-muted-foreground"
            }`}
          >
            Meal
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => onLogTypeChange("drink")}
          className={`flex-1 p-3 rounded-lg border-2 ${
            logType === "drink"
              ? "border-primary bg-primary/10"
              : "border-border bg-background"
          }`}
        >
          <Text
            className={`text-center font-medium ${
              logType === "drink" ? "text-primary" : "text-muted-foreground"
            }`}
          >
            Drink
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );
};
