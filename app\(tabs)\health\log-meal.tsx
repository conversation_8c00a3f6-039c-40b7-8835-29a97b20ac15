import React, { useState } from "react";
import { View, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { useMutation } from "convex/react";
import { X } from "lucide-react-native";

import { api } from "@/convex/_generated/api";
import { useImageUpload } from "@/hooks/useImageUpload";
import {
  analyzeNutritionFromImage,
  type NutritionAnalysis,
} from "@/utils/nutritionAnalysis";
import { Text } from "@/components/ui/text";
import {
  SetupScreen,
  AnalyzingScreen,
  NutritionReviewScreen,
  SuccessScreen,
} from "@/components/log-meal";

type MealType = "breakfast" | "lunch" | "dinner" | "snacks";
type LogType = "meal" | "drink";

// Use the NutritionAnalysis type from the utility
type NutritionData = NutritionAnalysis;

const LogMeal = () => {
  const router = useRouter();
  const { uploadImage } = useImageUpload();
  const createMeal = useMutation(api.meals.createMeal);
  const createDrink = useMutation(api.drinks.createDrink);
  const createMealWithImage = useMutation(api.meals.createMealWithImage);

  // State management
  const [step, setStep] = useState<"setup" | "analyze" | "review" | "success">(
    "setup"
  );
  const [logType, setLogType] = useState<LogType>("meal");
  const [mealType, setMealType] = useState<MealType>("breakfast");
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [nutritionData, setNutritionData] = useState<NutritionData | null>(
    null
  );
  const [isLogging, setIsLogging] = useState(false);

  const handleImageSelected = (uri: string) => {
    setSelectedImage(uri);
  };

  const handleAnalyze = async () => {
    if (!selectedImage) return;

    setStep("analyze");
    setIsAnalyzing(true);
    try {
      // Convert image to blob for upload
      const response = await fetch(selectedImage);
      const blob = await response.blob();

      setIsUploading(true);
      const uploadedImageUrl = await uploadImage(blob, {
        onProgress: (progress) => setUploadProgress(progress.percentage),
      });
      setIsUploading(false);

      // Use the nutrition analysis utility
      const nutritionAnalysis = await analyzeNutritionFromImage(
        uploadedImageUrl,
        logType
      );

      setNutritionData(nutritionAnalysis);
      setStep("review");
    } catch (error) {
      console.error("Error analyzing image:", error);
      setStep("setup"); // Go back to setup on error
    } finally {
      setIsAnalyzing(false);
      setIsUploading(false);
    }
  };

  const handleLogMeal = async () => {
    if (!nutritionData) return;

    setIsLogging(true);
    try {
      const today = new Date().toISOString().split("T")[0];

      if (logType === "meal") {
        await createMeal({
          name: nutritionData.name,
          description: nutritionData.description,
          calories: nutritionData.calories,
          protein: nutritionData.protein,
          carbs: nutritionData.carbs,
          fat: nutritionData.fat,
          fiber: nutritionData.fiber,
          sugar: nutritionData.sugar,
          sodium: nutritionData.sodium,
          water: nutritionData.water,
          calcium: nutritionData.calcium,
          iron: nutritionData.iron,
          vitaminC: nutritionData.vitaminC,
          vitaminD: nutritionData.vitaminD,
          type: mealType,
          date: today,
        });
      } else {
        await createDrink({
          name: nutritionData.name,
          description: nutritionData.description,
          calories: nutritionData.calories,
          protein: nutritionData.protein,
          carbs: nutritionData.carbs,
          fat: nutritionData.fat,
          fiber: nutritionData.fiber,
          sugar: nutritionData.sugar,
          sodium: nutritionData.sodium,
          water: nutritionData.water,
          calcium: nutritionData.calcium,
          iron: nutritionData.iron,
          vitaminC: nutritionData.vitaminC,
          vitaminD: nutritionData.vitaminD,
          type: mealType,
          date: today,
        });
      }

      setStep("success");
    } catch (error) {
      console.error("Error logging meal:", error);
      Alert.alert("Error", "Failed to log meal. Please try again.");
    } finally {
      setIsLogging(false);
    }
  };

  const resetForm = () => {
    setStep("setup");
    setSelectedImage(null);
    setNutritionData(null);
    setUploadProgress(0);
    setIsUploading(false);
    setIsAnalyzing(false);
    setIsLogging(false);
  };



  const renderAnalyze = () => (
    <View className="flex-1 p-6">
      <Text className="text-2xl font-bold mb-2">Analyzing Image</Text>
      <Text className="text-muted-foreground mb-8">
        Please wait while our AI analyzes your {logType} image...
      </Text>

      {selectedImage && (
        <Card className="p-4 mb-6">
          <Image
            source={{ uri: selectedImage }}
            className="w-full h-48 rounded-lg mb-4"
            resizeMode="cover"
          />
        </Card>
      )}

      {isUploading && (
        <Card className="p-6 mb-6">
          <Text className="text-lg font-semibold mb-4">Uploading Image</Text>
          <Progress value={uploadProgress} className="mb-2" />
          <Text className="text-sm text-muted-foreground">
            {uploadProgress}% complete
          </Text>
        </Card>
      )}

      {isAnalyzing && (
        <Card className="p-6 mb-6">
          <View className="items-center">
            <Text className="text-lg font-semibold mb-2">
              Analyzing Nutrition
            </Text>
            <Text className="text-muted-foreground text-center">
              Our AI is identifying ingredients and calculating nutrition
              values...
            </Text>
          </View>
        </Card>
      )}
    </View>
  );

  const renderReview = () => (
    <View className="flex-1 p-6">
      <Text className="text-2xl font-bold mb-2">Review Nutrition</Text>
      <Text className="text-muted-foreground mb-6">
        Review and edit the nutrition information before logging.
      </Text>

      {selectedImage && (
        <Card className="p-4 mb-6">
          <Image
            source={{ uri: selectedImage }}
            className="w-full h-32 rounded-lg"
            resizeMode="cover"
          />
        </Card>
      )}

      {nutritionData && (
        <ScrollView
          className="flex-1 mb-6"
          showsVerticalScrollIndicator={false}
        >
          <Card className="p-6 mb-4">
            <Text className="text-lg font-semibold mb-4">
              Basic Information
            </Text>
            <View className="space-y-4">
              <View>
                <Text className="text-sm font-medium mb-2">Name</Text>
                <Input
                  value={nutritionData.name}
                  onChangeText={(text) =>
                    setNutritionData({ ...nutritionData, name: text })
                  }
                  placeholder="Enter meal name"
                />
              </View>
              <View>
                <Text className="text-sm font-medium mb-2">Description</Text>
                <Textarea
                  value={nutritionData.description}
                  onChangeText={(text) =>
                    setNutritionData({ ...nutritionData, description: text })
                  }
                  placeholder="Enter description"
                  numberOfLines={3}
                />
              </View>
            </View>
          </Card>

          <Card className="p-6 mb-4">
            <Text className="text-lg font-semibold mb-4">Macronutrients</Text>
            <View className="grid grid-cols-2 gap-4">
              <View>
                <Text className="text-sm font-medium mb-2">Calories</Text>
                <Input
                  value={nutritionData.calories.toString()}
                  onChangeText={(text) =>
                    setNutritionData({
                      ...nutritionData,
                      calories: parseFloat(text) || 0,
                    })
                  }
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>
              <View>
                <Text className="text-sm font-medium mb-2">Protein (g)</Text>
                <Input
                  value={nutritionData.protein.toString()}
                  onChangeText={(text) =>
                    setNutritionData({
                      ...nutritionData,
                      protein: parseFloat(text) || 0,
                    })
                  }
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>
              <View>
                <Text className="text-sm font-medium mb-2">Carbs (g)</Text>
                <Input
                  value={nutritionData.carbs.toString()}
                  onChangeText={(text) =>
                    setNutritionData({
                      ...nutritionData,
                      carbs: parseFloat(text) || 0,
                    })
                  }
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>
              <View>
                <Text className="text-sm font-medium mb-2">Fat (g)</Text>
                <Input
                  value={nutritionData.fat.toString()}
                  onChangeText={(text) =>
                    setNutritionData({
                      ...nutritionData,
                      fat: parseFloat(text) || 0,
                    })
                  }
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>
            </View>
          </Card>

          <Card className="p-6">
            <Text className="text-lg font-semibold mb-4">
              Additional Nutrients
            </Text>
            <View className="grid grid-cols-2 gap-4">
              <View>
                <Text className="text-sm font-medium mb-2">Fiber (g)</Text>
                <Input
                  value={nutritionData.fiber?.toString() || ""}
                  onChangeText={(text) =>
                    setNutritionData({
                      ...nutritionData,
                      fiber: parseFloat(text) || 0,
                    })
                  }
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>
              <View>
                <Text className="text-sm font-medium mb-2">Sugar (g)</Text>
                <Input
                  value={nutritionData.sugar?.toString() || ""}
                  onChangeText={(text) =>
                    setNutritionData({
                      ...nutritionData,
                      sugar: parseFloat(text) || 0,
                    })
                  }
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>
              <View>
                <Text className="text-sm font-medium mb-2">Sodium (mg)</Text>
                <Input
                  value={nutritionData.sodium?.toString() || ""}
                  onChangeText={(text) =>
                    setNutritionData({
                      ...nutritionData,
                      sodium: parseFloat(text) || 0,
                    })
                  }
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>
              <View>
                <Text className="text-sm font-medium mb-2">Water (ml)</Text>
                <Input
                  value={nutritionData.water?.toString() || ""}
                  onChangeText={(text) =>
                    setNutritionData({
                      ...nutritionData,
                      water: parseFloat(text) || 0,
                    })
                  }
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>
            </View>
          </Card>
        </ScrollView>
      )}

      <View className="flex-row space-x-3">
        <Button
          onPress={() => setStep("setup")}
          variant="outline"
          size="lg"
          className="flex-1"
        >
          <Text>Back</Text>
        </Button>
        <Button
          onPress={handleLogMeal}
          disabled={isLogging}
          size="lg"
          className="flex-1"
        >
          <Text className="text-white font-semibold">
            {isLogging ? "Logging..." : `Log ${logType}`}
          </Text>
        </Button>
      </View>
    </View>
  );

  const renderSuccess = () => (
    <View className="flex-1 p-6 justify-center items-center">
      <View className="items-center mb-8">
        <View className="w-20 h-20 bg-green-100 rounded-full items-center justify-center mb-4">
          <Check size={40} className="text-green-600" />
        </View>
        <Text className="text-2xl font-bold mb-2">Successfully Logged!</Text>
        <Text className="text-muted-foreground text-center">
          Your {logType} has been added to your nutrition tracking.
        </Text>
      </View>

      <View className="w-full space-y-3">
        <Button onPress={resetForm} size="lg" className="w-full">
          <Text className="text-white font-semibold">
            Log Another {logType}
          </Text>
        </Button>
        <Button
          onPress={() => router.back()}
          variant="outline"
          size="lg"
          className="w-full"
        >
          <Text>Back to Health</Text>
        </Button>
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-background">
      <View className="flex-row items-center justify-between p-4 border-b border-border">
        <TouchableOpacity onPress={() => router.back()}>
          <X size={24} className="text-foreground" />
        </TouchableOpacity>
        <Text className="text-lg font-semibold">Log {logType}</Text>
        <View className="w-6" />
      </View>

      {step === "setup" && renderSetup()}
      {step === "analyze" && renderAnalyze()}
      {step === "review" && renderReview()}
      {step === "success" && renderSuccess()}
    </SafeAreaView>
  );
};

export default LogMeal;
